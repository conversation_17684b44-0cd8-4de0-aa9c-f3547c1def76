import { getAdminClient } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * API endpoint for admin service management
 * This endpoint uses service_role key to bypass RLS policies
 * Uses the simplified authentication approach
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);

  // Log the incoming request
  console.log(`[${requestId}] Processing ${req.method} request to /api/admin/services`);

  try {
    // Normal authentication flow with enhanced error handling
    console.log(`[${requestId}] Starting authentication flow for ${req.url}`);

    let authResult;
    try {
      authResult = await authenticateAdminRequest(req);
    } catch (authError) {
      console.error(`[${requestId}] Authentication system error:`, authError);
      return res.status(500).json({
        error: 'Authentication system error',
        message: 'Unable to verify authentication. Please try again.',
        requestId
      });
    }

    const { authorized, error, user, role } = authResult;

    if (!authorized) {
      console.error(`[${requestId}] Authentication failed: ${error?.message || 'Unknown error'}`);
      return res.status(401).json({
        error: 'Unauthorized access',
        message: error?.message || 'Authentication failed',
        requestId
      });
    }

    console.log(`[${requestId}] Authentication successful for user: ${user?.email}, role: ${role}`);

    // User is authenticated, proceed with the request
    return await processRequest(req, res, user, role, requestId);
  } catch (systemError) {
    console.error(`[${requestId}] System error in services handler:`, systemError);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'A system error occurred. Please try again.',
      requestId
    });
  }
}

/**
 * Process the API request after authentication
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @param {Object} user - Authenticated user
 * @param {string} role - User role
 * @param {string} requestId - Unique request ID for tracking
 * @returns {Object} - JSON response
 */
async function processRequest(req, res, user, role, requestId = null) {
  // Generate a request ID if not provided
  const reqId = requestId || Math.random().toString(36).substring(2, 8);
  // Check if the request method is allowed
  if (!['GET', 'POST', 'PUT', 'DELETE'].includes(req.method)) {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // GET - Fetch services
    if (req.method === 'GET') {
      console.log(`[${reqId}] Fetching services`);

      try {
        // Get admin client with enhanced error handling
        let adminClient;
        try {
          adminClient = getAdminClient();
          console.log(`[${reqId}] Admin client initialized successfully`);
        } catch (clientError) {
          console.error(`[${reqId}] Failed to initialize admin client:`, clientError);

          // Provide more specific error information
          const errorMessage = clientError.message || 'Unknown database connection error';
          if (errorMessage.includes('SUPABASE_SERVICE_ROLE_KEY')) {
            return res.status(500).json({
              error: 'Database configuration error',
              message: 'Service configuration is missing. Please contact support.',
              requestId: reqId
            });
          }

          return res.status(500).json({
            error: 'Database connection failed',
            message: 'Unable to connect to database. Please try again.',
            requestId: reqId
          });
        }

        // Test database connection before proceeding
        try {
          await adminClient.from('services').select('count', { count: 'exact', head: true });
          console.log(`[${reqId}] Database connection verified`);
        } catch (connectionError) {
          console.error(`[${reqId}] Database connection test failed:`, connectionError);
          return res.status(500).json({
            error: 'Database connection failed',
            message: 'Database is not responding. Please try again.',
            requestId: reqId
          });
        }

        // Execute the query - fetch services with pricing tiers for consistency with public API
        const { data, error } = await adminClient
          .from('services_with_pricing')
          .select('*')
          .order('name');

        if (error) {
          console.error(`[${reqId}] Error fetching services:`, error);

          // Handle specific database errors
          if (error.code === 'PGRST301') {
            return res.status(404).json({
              error: 'Services table not found',
              message: 'Database schema issue. Please contact support.',
              requestId: reqId
            });
          }

          if (error.message && error.message.includes('permission')) {
            return res.status(403).json({
              error: 'Database permission error',
              message: 'Insufficient permissions to access services.',
              requestId: reqId
            });
          }

          throw error;
        }

        // Ensure all data is properly serialized - include pricing tiers for consistency
        // Apply same image fallback logic as public API for consistency
        // CRITICAL: Convert ALL data to primitive types to prevent React Error #130
        const serializedServices = data?.map(service => ({
          id: String(service.id || ''),
          name: String(service.name || ''),
          description: String(service.description || ''),
          duration: String(service.duration || '0'),
          price: String(service.price || '0'),
          color: String(service.color || '#6a0dad'),
          category: String(service.category || ''),
          image_url: String(service.image_url || '/images/placeholder.svg'), // Use existing placeholder.svg as fallback
          status: String(service.status || 'active'),
          featured: String(service.featured || 'false'), // Convert boolean to string
          created_at: String(service.created_at || ''),
          updated_at: String(service.updated_at || ''),
          // CRITICAL: Convert pricing_tiers to serializable format
          pricing_tiers: Array.isArray(service.pricing_tiers)
            ? service.pricing_tiers.map(tier => ({
                id: String(tier.id || ''),
                name: String(tier.name || ''),
                description: String(tier.description || ''),
                duration: String(tier.duration || '0'),
                price: String(tier.price || '0'),
                is_default: String(tier.is_default || 'false'),
                sort_order: String(tier.sort_order || '0')
              }))
            : []
        })) || [];

        console.log(`[${reqId}] Successfully fetched ${serializedServices.length} services`);
        return res.status(200).json({ services: serializedServices });
      } catch (error) {
        console.error(`[${reqId}] Error in GET services:`, error);
        throw error; // Will be caught by the outer try/catch
      }
    }

    // POST - Create a new service
    if (req.method === 'POST') {
      // Generate a unique request ID for tracking
      const requestId = Math.random().toString(36).substring(2, 8);
      console.log(`[${requestId}] Creating new service`);

      try {
        const {
          name,
          description,
          duration,
          price,
          color,
          category,
          image_url,
          status,
          featured,
          pricingTiers
        } = req.body;

        // Validate required fields
        if (!name) {
          console.warn(`[${requestId}] Missing required fields for service creation`);
          return res.status(400).json({ error: 'Service name is required' });
        }

        // Validate pricing tiers if provided
        if (pricingTiers && pricingTiers.length > 0) {
          const hasDefault = pricingTiers.some(tier => tier.is_default);
          if (!hasDefault) {
            return res.status(400).json({ error: 'One pricing tier must be marked as default' });
          }
        }

        // Get admin client
        let adminClient;
        try {
          adminClient = getAdminClient();
          console.log(`[${requestId}] Admin client initialized successfully`);
        } catch (clientError) {
          console.error(`[${requestId}] Failed to initialize admin client:`, clientError);
          return res.status(500).json({ error: 'Database connection failed', message: clientError.message });
        }

        // Create the service
        const { data, error } = await adminClient
          .from('services')
          .insert([{
            name,
            description: description || '',
            duration: parseInt(duration, 10),
            price: parseFloat(price),
            color: color || '#6a0dad',
            category: category || '',
            image_url: image_url || '',
            status: status || 'active',
            featured: featured || false
          }])
          .select();

        if (error) {
          console.error(`[${requestId}] Error creating service:`, error);
          throw error;
        }

        const serviceId = data[0].id;

        // Handle pricing tiers if provided
        if (pricingTiers && pricingTiers.length > 0) {
          const tiersToInsert = pricingTiers.map(tier => ({
            service_id: serviceId,
            name: tier.name,
            description: tier.description || '',
            duration: tier.duration,
            price: tier.price,
            is_default: tier.is_default,
            sort_order: tier.sort_order
          }));

          const { error: insertError } = await adminClient
            .from('service_pricing_tiers')
            .insert(tiersToInsert);

          if (insertError) {
            console.error(`[${requestId}] Error inserting pricing tiers:`, insertError);
            throw insertError;
          }

          console.log(`[${requestId}] Pricing tiers created for service: ${serviceId}`);
        }

        console.log(`[${requestId}] Service created successfully: ${name}`);
        return res.status(201).json({ service: data[0] });
      } catch (error) {
        console.error(`[${requestId}] Error in POST service:`, error);
        throw error; // Will be caught by the outer try/catch
      }
    }

    // PUT - Update an existing service
    if (req.method === 'PUT') {
      // Generate a unique request ID for tracking
      const requestId = Math.random().toString(36).substring(2, 8);
      console.log(`[${requestId}] Updating service`);

      try {
        const { id } = req.query;
        const {
          name,
          description,
          duration,
          price,
          color
        } = req.body;

        // Validate service ID
        if (!id) {
          console.warn(`[${requestId}] Missing service ID for update`);
          return res.status(400).json({ error: 'Service ID is required' });
        }

        // Get admin client
        let adminClient;
        try {
          adminClient = getAdminClient();
          console.log(`[${requestId}] Admin client initialized successfully`);
        } catch (clientError) {
          console.error(`[${requestId}] Failed to initialize admin client:`, clientError);
          return res.status(500).json({ error: 'Database connection failed', message: clientError.message });
        }

        // Update the service
        const { data, error } = await adminClient
          .from('services')
          .update({
            name,
            description: description || '',
            duration: parseInt(duration, 10),
            price: parseFloat(price),
            color: color || '#6a0dad'
          })
          .eq('id', id)
          .select();

        if (error) {
          console.error(`[${requestId}] Error updating service:`, error);
          throw error;
        }

        if (!data || data.length === 0) {
          console.warn(`[${requestId}] Service not found for update: ${id}`);
          return res.status(404).json({ error: 'Service not found' });
        }

        console.log(`[${requestId}] Service updated successfully: ${id}`);
        return res.status(200).json({ service: data[0] });
      } catch (error) {
        console.error(`[${requestId}] Error in PUT service:`, error);
        throw error; // Will be caught by the outer try/catch
      }
    }

    // DELETE - Delete a service
    if (req.method === 'DELETE') {
      // Generate a unique request ID for tracking
      const requestId = Math.random().toString(36).substring(2, 8);
      console.log(`[${requestId}] Deleting service`);

      try {
        const { id } = req.query;

        // Validate service ID
        if (!id) {
          console.warn(`[${requestId}] Missing service ID for deletion`);
          return res.status(400).json({ error: 'Service ID is required' });
        }

        // Get admin client
        let adminClient;
        try {
          adminClient = getAdminClient();
          console.log(`[${requestId}] Admin client initialized successfully`);
        } catch (clientError) {
          console.error(`[${requestId}] Failed to initialize admin client:`, clientError);
          return res.status(500).json({ error: 'Database connection failed', message: clientError.message });
        }

        // Check if service is used in any bookings
        const { data: bookings, error: bookingsError } = await adminClient
          .from('bookings')
          .select('id')
          .eq('service_id', id)
          .limit(1);

        if (bookingsError) {
          console.error(`[${requestId}] Error checking service usage:`, bookingsError);
          throw bookingsError;
        }

        // Prevent deletion if service is used in bookings
        if (bookings && bookings.length > 0) {
          console.warn(`[${requestId}] Cannot delete service ${id} - used in bookings`);
          return res.status(400).json({
            error: 'Cannot delete service that is used in bookings'
          });
        }

        // Delete the service
        const { error } = await adminClient
          .from('services')
          .delete()
          .eq('id', id);

        if (error) {
          console.error(`[${requestId}] Error deleting service:`, error);
          throw error;
        }

        console.log(`[${requestId}] Service deleted successfully: ${id}`);
        return res.status(200).json({ success: true });
      } catch (error) {
        console.error(`[${requestId}] Error in DELETE service:`, error);
        throw error; // Will be caught by the outer try/catch
      }
    }
  } catch (error) {
    // Generate a unique request ID for tracking if not already generated
    const requestId = Math.random().toString(36).substring(2, 8);
    console.error(`[${requestId}] Service API Error:`, error);

    // Determine the appropriate status code based on the error
    let statusCode = 500;
    let errorMessage = 'Failed to process service request';

    if (error.message && error.message.includes('not found')) {
      statusCode = 404; // Not Found
      errorMessage = 'Requested resource not found';
    } else if (error.message && (
      error.message.includes('permission') ||
      error.message.includes('access') ||
      error.message.includes('unauthorized')
    )) {
      statusCode = 403; // Forbidden
      errorMessage = 'Permission denied';
    } else if (error.message && error.message.includes('validation')) {
      statusCode = 400; // Bad Request
      errorMessage = 'Validation error';
    } else if (error.message && error.message.includes('duplicate')) {
      statusCode = 409; // Conflict
      errorMessage = 'Resource already exists';
    } else if (error.message && error.message.includes('timeout')) {
      statusCode = 504; // Gateway Timeout
      errorMessage = 'Request timed out';
    }

    // In development mode, provide more detailed error information
    const errorResponse = {
      error: errorMessage,
      message: error.message || 'An error occurred while processing your request',
      timestamp: new Date().toISOString(),
      requestId
    };

    // Add stack trace in development mode
    if (process.env.NODE_ENV === 'development') {
      errorResponse.details = error.stack;
      errorResponse.code = error.code;
    }

    return res.status(statusCode).json(errorResponse);
  }
}
