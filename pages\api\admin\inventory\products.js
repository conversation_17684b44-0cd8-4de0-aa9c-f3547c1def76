import { supabaseAdmin } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';
import { resolveImageUrl } from '@/lib/image-utils';

/**
 * API endpoint for product management
 * Uses the simplified authentication approach
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Authenticate request using our simplified auth module
  const { authorized, error, user, role } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed'
    });
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getProducts(req, res);
    case 'POST':
      return createProduct(req, res);
    case 'PUT':
      return updateProduct(req, res);
    case 'DELETE':
      return deleteProduct(req, res);
    default:
      return res.status(405).json({ error: 'Method not allowed' });
  }
}

/**
 * Get products with optional filters
 */
async function getProducts(req, res) {
  const {
    search,
    category,
    stock_status,
    sort_by = 'created_at',
    sort_order = 'desc'
  } = req.query;
  try {
    // Start building the query using Supabase directly
    let query = supabaseAdmin
      .from('products')
      .select('*', { count: 'exact' });

    // Apply search filter
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%,sku.ilike.%${search}%`);
    }

    // Apply category filter
    if (category) {
      query = query.eq('category', category);
    }

    // Apply stock status filter
    if (stock_status) {
      switch (stock_status) {
        case 'in_stock':
          query = query.gt('stock', 0);
          break;
        case 'low_stock':
          query = query.lte('stock', 10).gt('stock', 0);
          break;
        case 'out_of_stock':
          query = query.eq('stock', 0);
          break;
      }
    }

    // Apply sorting
    query = query.order(sort_by, { ascending: sort_order === 'asc' });

    // Execute the query
    const { data: products, error } = await query;

    if (error) {
      throw error;
    }

    // Ensure all data is properly serialized to prevent object rendering errors
    const serializedProducts = products?.map(product => ({
      id: product.id,
      name: String(product.name || ''),
      description: String(product.description || ''),
      price: Number(product.price) || 0,
      sale_price: product.sale_price ? Number(product.sale_price) : null,
      cost_price: product.cost_price ? Number(product.cost_price) : null,
      sku: String(product.sku || ''),
      stock: Number(product.stock) || 0,
      low_stock_threshold: Number(product.low_stock_threshold) || 0,
      category: String(product.category || ''),
      category_id: product.category_id,
      supplier_id: product.supplier_id,
      image_url: resolveImageUrl(product.image_url, '/images/products/default-product.jpg'), // Fix image URL resolution with same fallback as public API
      status: String(product.status || 'active'),
      is_active: Boolean(product.is_active),
      featured: Boolean(product.featured),
      weight: product.weight ? Number(product.weight) : null,
      dimensions: product.dimensions ? String(product.dimensions) : null,
      created_at: product.created_at,
      updated_at: product.updated_at
    })) || [];

    return res.status(200).json({ products: serializedProducts });
  } catch (error) {
    console.error('Error fetching products:', error);

    // Determine the appropriate status code based on the error
    let statusCode = 500;
    let errorMessage = 'Failed to fetch products';

    if (error.message && error.message.includes('not found')) {
      statusCode = 404; // Not Found
      errorMessage = 'Products not found';
    } else if (error.message && (
      error.message.includes('permission') ||
      error.message.includes('access') ||
      error.message.includes('unauthorized')
    )) {
      statusCode = 403; // Forbidden
      errorMessage = 'Permission denied';
    } else if (error.message && error.message.includes('validation')) {
      statusCode = 400; // Bad Request
      errorMessage = 'Validation error';
    }

    return res.status(statusCode).json({
      error: errorMessage,
      message: error.message || 'An error occurred while processing your request',
      timestamp: new Date().toISOString(),
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
}

/**
 * Create a new product
 */
async function createProduct(req, res) {
  try {
    const productData = req.body;

    // Debug: Log the incoming data
    console.log('Create product data received:', JSON.stringify(productData, null, 2));

    // Extract and validate required fields
    const { name, price } = productData;
    if (!name || price === undefined) {
      return res.status(400).json({ error: 'Name and price are required fields' });
    }

    // Handle field mapping and UUID fields
    // Map 'category' to 'category_id' if it exists
    if (productData.category !== undefined) {
      productData.category_id = productData.category === '' ? null : productData.category;
      delete productData.category;
    }

    // Handle other UUID fields - convert empty strings to null
    const uuidFields = ['category_id', 'supplier_id'];
    uuidFields.forEach(field => {
      if (productData[field] === '') {
        productData[field] = null;
      }
    });

    // Set default values and ensure consistency
    productData.status = productData.status || 'active';
    productData.featured = productData.featured || false;

    // Ensure is_active field is consistent with status field
    productData.is_active = productData.status === 'active';

    // Debug: Log the processed data
    console.log('Processed create product data:', JSON.stringify(productData, null, 2));

    // Create product in database using Supabase directly
    const { data, error } = await supabaseAdmin
      .from('products')
      .insert([productData])
      .select();

    if (error) {
      throw error;
    }

    return res.status(201).json({ product: data[0] });
  } catch (error) {
    console.error('Error creating product:', error);
    return res.status(500).json({
      error: 'Failed to create product',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
}

/**
 * Update an existing product
 */
async function updateProduct(req, res) {
  try {
    const { id, ...updateData } = req.body;

    // Validate required fields
    if (!id) {
      return res.status(400).json({ error: 'Product ID is required' });
    }

    // Handle field mapping and UUID fields
    // Map 'category' to 'category_id' if it exists
    if (updateData.category !== undefined) {
      updateData.category_id = updateData.category === '' ? null : updateData.category;
      delete updateData.category;
    }

    // Handle other UUID fields - convert empty strings to null
    const uuidFields = ['category_id', 'supplier_id'];
    uuidFields.forEach(field => {
      if (updateData[field] === '') {
        updateData[field] = null;
      }
    });

    // Ensure is_active field is consistent with status field
    if (updateData.status !== undefined) {
      updateData.is_active = updateData.status === 'active';
    }

    // Update product in database using Supabase directly
    const { data, error } = await supabaseAdmin
      .from('products')
      .update(updateData)
      .eq('id', id)
      .select();

    if (error) {
      throw error;
    }

    return res.status(200).json({ product: data[0] });
  } catch (error) {
    console.error('Error updating product:', error);
    return res.status(500).json({
      error: 'Failed to update product',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
}

/**
 * Delete a product
 */
async function deleteProduct(req, res) {
  try {
    const { id } = req.query;

    // Validate required fields
    if (!id) {
      return res.status(400).json({ error: 'Product ID is required' });
    }    // Delete product from database using Supabase directly
    const { error } = await supabaseAdmin
      .from('products')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    return res.status(200).json({ message: 'Product deleted successfully' });
  } catch (error) {
    console.error('Error deleting product:', error);
    return res.status(500).json({
      error: 'Failed to delete product',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
}
