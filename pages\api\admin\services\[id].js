import { getAdminClient } from '@/lib/supabase';
import authTokenManager from '@/lib/auth-token-manager';

/**
 * API endpoint for individual service management
 * This endpoint handles GET, PUT, and DELETE operations for specific services
 * Uses service_role key to bypass RLS policies
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);

  // Log the incoming request
  console.log(`[${requestId}] Processing ${req.method} request to /api/admin/services/[id]`);

  // Normal authentication flow
  console.log(`[${requestId}] Starting normal authentication flow for ${req.url}`);
  const authResult = await authTokenManager.verifyToken(req);

  if (!authResult.valid) {
    console.error(`[${requestId}] Authentication failed: ${authResult.error}`);
    return res.status(401).json({
      error: 'Unauthorized access',
      message: authResult.error || 'Authentication failed',
      requestId
    });
  }

  console.log(`[${requestId}] Authentication successful for user: ${authResult.user?.email}, role: ${authResult.user?.role}`);

  // User is authenticated, proceed with the request
  return await processRequest(req, res, authResult.user, authResult.user?.role, requestId);
}

/**
 * Process the API request after authentication
 */
async function processRequest(req, res, user, role, requestId = null) {
  const reqId = requestId || Math.random().toString(36).substring(2, 8);

  // Check if the request method is allowed
  if (!['GET', 'PUT', 'DELETE'].includes(req.method)) {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ error: 'Service ID is required' });
  }

  try {
    // Get admin client
    let adminClient;
    try {
      adminClient = getAdminClient();
      console.log(`[${requestId}] Admin client initialized successfully`);
    } catch (clientError) {
      console.error(`[${requestId}] Failed to initialize admin client:`, clientError);
      return res.status(500).json({ error: 'Database connection failed', message: clientError.message });
    }

    if (req.method === 'GET') {
      // Fetch individual service with pricing tiers
      const { data, error } = await adminClient
        .from('services_with_pricing')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error(`[${requestId}] Error fetching service:`, error);
        if (error.code === 'PGRST116') {
          return res.status(404).json({ error: 'Service not found' });
        }
        throw error;
      }

      // Apply same image fallback logic as public API for consistency
      const serializedService = {
        ...data,
        image_url: String(data.image_url || '/images/services/face-paint.jpg')
      };

      console.log(`[${requestId}] Service fetched successfully: ${id}`);
      return res.status(200).json({ service: serializedService });

    } else if (req.method === 'PUT') {
      // Update service
      const {
        name,
        description,
        duration,
        price,
        color,
        category,
        image_url,
        status,
        featured,
        pricingTiers
      } = req.body;

      // Validate required fields
      if (!name) {
        console.warn(`[${requestId}] Missing required fields for service update`);
        return res.status(400).json({ error: 'Service name is required' });
      }

      // Validate pricing tiers if provided
      if (pricingTiers && pricingTiers.length > 0) {
        const hasDefault = pricingTiers.some(tier => tier.is_default);
        if (!hasDefault) {
          return res.status(400).json({ error: 'One pricing tier must be marked as default' });
        }
      }

      // Update the service
      const { data, error } = await adminClient
        .from('services')
        .update({
          name,
          description: description || '',
          duration: parseInt(duration, 10),
          price: parseFloat(price),
          color: color || '#6a0dad',
          category: category || '',
          image_url: image_url || '',
          status: status || 'active',
          featured: featured || false
        })
        .eq('id', id)
        .select();

      if (error) {
        console.error(`[${requestId}] Error updating service:`, error);
        throw error;
      }

      if (!data || data.length === 0) {
        console.warn(`[${requestId}] Service not found for update: ${id}`);
        return res.status(404).json({ error: 'Service not found' });
      }

      // Handle pricing tiers if provided
      if (pricingTiers && pricingTiers.length > 0) {
        // Delete existing pricing tiers
        const { error: deleteError } = await adminClient
          .from('service_pricing_tiers')
          .delete()
          .eq('service_id', id);

        if (deleteError) {
          console.error(`[${requestId}] Error deleting existing pricing tiers:`, deleteError);
          throw deleteError;
        }

        // Insert new pricing tiers
        const tiersToInsert = pricingTiers.map(tier => ({
          service_id: id,
          name: tier.name,
          description: tier.description || '',
          duration: tier.duration,
          price: tier.price,
          is_default: tier.is_default,
          sort_order: tier.sort_order
        }));

        const { error: insertError } = await adminClient
          .from('service_pricing_tiers')
          .insert(tiersToInsert);

        if (insertError) {
          console.error(`[${requestId}] Error inserting pricing tiers:`, insertError);
          throw insertError;
        }

        console.log(`[${requestId}] Pricing tiers updated for service: ${id}`);
      }

      console.log(`[${requestId}] Service updated successfully: ${id}`);
      return res.status(200).json({ service: data[0] });

    } else if (req.method === 'DELETE') {
      // Check if service is used in bookings before deletion
      const { data: bookings, error: bookingsError } = await adminClient
        .from('bookings')
        .select('id')
        .eq('service_id', id)
        .limit(1);

      if (bookingsError) {
        console.error(`[${requestId}] Error checking service usage:`, bookingsError);
        throw bookingsError;
      }

      // Prevent deletion if service is used in bookings
      if (bookings && bookings.length > 0) {
        console.warn(`[${requestId}] Cannot delete service ${id} - used in bookings`);
        return res.status(400).json({
          error: 'Cannot delete service that is used in bookings'
        });
      }

      // Delete the service
      const { error } = await adminClient
        .from('services')
        .delete()
        .eq('id', id);

      if (error) {
        console.error(`[${requestId}] Error deleting service:`, error);
        throw error;
      }

      console.log(`[${requestId}] Service deleted successfully: ${id}`);
      return res.status(200).json({ message: 'Service deleted successfully' });
    }

  } catch (error) {
    console.error(`[${requestId}] Error in service API:`, error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error.message,
      requestId
    });
  }
}
